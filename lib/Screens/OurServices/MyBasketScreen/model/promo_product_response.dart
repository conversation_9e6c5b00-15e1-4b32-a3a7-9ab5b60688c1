// ignore_for_file: public_member_api_docs, sort_constructors_first
class PromoProductResponse {
  bool? success;
  int? statusCode;
  String? message;
  List<PromoProductModel>? data;

  PromoProductResponse({this.success, this.statusCode, this.message, this.data});

  PromoProductResponse.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    statusCode = json['status_code'];
    message = json['message'];
    if (json['data'] != null) {
      data = <PromoProductModel>[];
      json['data'].forEach((v) {
        data!.add(new PromoProductModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['success'] = this.success;
    data['status_code'] = this.statusCode;
    data['message'] = this.message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class PromoProductModel {
  int? id;
  String? name;
  String? description;
  String? image;
  int? storeId;
  int? status;
  String? cost;
  String? regularPrice;
  int? stock;
  String? weightUnit;
  int qty = 0;

  PromoProductModel(
      {this.id,
      this.name,
      this.description,
      this.image,
      this.storeId,
      this.status,
      this.cost,
      this.regularPrice,
      this.stock,
      this.weightUnit,
      this.qty = 0});

  PromoProductModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    description = json['description'];
    image = json['image'];
    storeId = json['store_id'];
    status = json['status'];
    cost = json['cost'];
    regularPrice = json['regular_price'];
    stock = json['stock'];
    weightUnit = json['weight_unit'];
    qty = 0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['description'] = this.description;
    data['image'] = this.image;
    data['store_id'] = this.storeId;
    data['status'] = this.status;
    data['cost'] = this.cost;
    data['regular_price'] = this.regularPrice;
    data['stock'] = this.stock;
    data['weight_unit'] = this.weightUnit;
    return data;
  }

  PromoProductModel copyWith({
    int? id,
    String? name,
    String? description,
    String? image,
    int? storeId,
    int? status,
    String? cost,
    String? regularPrice,
    int? stock,
    String? weightUnit,
    int? qty,
  }) {
    return PromoProductModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      image: image ?? this.image,
      storeId: storeId ?? this.storeId,
      status: status ?? this.status,
      cost: cost ?? this.cost,
      regularPrice: regularPrice ?? this.regularPrice,
      stock: stock ?? this.stock,
      weightUnit: weightUnit ?? this.weightUnit,
      qty: qty ?? this.qty,
    );
  }

  double get totalPrice => double.parse(regularPrice ?? '0') * qty;
}
